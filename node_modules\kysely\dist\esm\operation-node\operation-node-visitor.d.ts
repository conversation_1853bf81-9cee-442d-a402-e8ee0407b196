import { AliasNode } from '../operation-node/alias-node.js';
import { ColumnNode } from '../operation-node/column-node.js';
import { IdentifierNode } from '../operation-node/identifier-node.js';
import { OperationNode } from '../operation-node/operation-node.js';
import { ReferenceNode } from '../operation-node/reference-node.js';
import { SelectAllNode } from '../operation-node/select-all-node.js';
import { SelectionNode } from '../operation-node/selection-node.js';
import { TableNode } from '../operation-node/table-node.js';
import { AndNode } from './and-node.js';
import { JoinNode } from './join-node.js';
import { OrNode } from './or-node.js';
import { ParensNode } from './parens-node.js';
import { PrimitiveValueListNode } from './primitive-value-list-node.js';
import { RawNode } from './raw-node.js';
import { SelectQueryNode } from './select-query-node.js';
import { ValueListNode } from './value-list-node.js';
import { ValueNode } from './value-node.js';
import { OperatorNode } from './operator-node.js';
import { FromNode } from './from-node.js';
import { WhereNode } from './where-node.js';
import { InsertQueryNode } from './insert-query-node.js';
import { DeleteQueryNode } from './delete-query-node.js';
import { ReturningNode } from './returning-node.js';
import { CreateTableNode } from './create-table-node.js';
import { AddColumnNode } from './add-column-node.js';
import { DropTableNode } from './drop-table-node.js';
import { DataTypeNode } from './data-type-node.js';
import { OrderByNode } from './order-by-node.js';
import { OrderByItemNode } from './order-by-item-node.js';
import { GroupByNode } from './group-by-node.js';
import { GroupByItemNode } from './group-by-item-node.js';
import { UpdateQueryNode } from './update-query-node.js';
import { ColumnUpdateNode } from './column-update-node.js';
import { LimitNode } from './limit-node.js';
import { OffsetNode } from './offset-node.js';
import { OnConflictNode } from './on-conflict-node.js';
import { CreateIndexNode } from './create-index-node.js';
import { ListNode } from './list-node.js';
import { DropIndexNode } from './drop-index-node.js';
import { PrimaryKeyConstraintNode } from './primary-key-constraint-node.js';
import { UniqueConstraintNode } from './unique-constraint-node.js';
import { ReferencesNode } from './references-node.js';
import { CheckConstraintNode } from './check-constraint-node.js';
import { WithNode } from './with-node.js';
import { CommonTableExpressionNode } from './common-table-expression-node.js';
import { CommonTableExpressionNameNode } from './common-table-expression-name-node.js';
import { HavingNode } from './having-node.js';
import { CreateSchemaNode } from './create-schema-node.js';
import { DropSchemaNode } from './drop-schema-node.js';
import { AlterTableNode } from './alter-table-node.js';
import { DropColumnNode } from './drop-column-node.js';
import { RenameColumnNode } from './rename-column-node.js';
import { AlterColumnNode } from './alter-column-node.js';
import { AddConstraintNode } from './add-constraint-node.js';
import { DropConstraintNode } from './drop-constraint-node.js';
import { ForeignKeyConstraintNode } from './foreign-key-constraint-node.js';
import { ColumnDefinitionNode } from './column-definition-node.js';
import { ModifyColumnNode } from './modify-column-node.js';
import { OnDuplicateKeyNode } from './on-duplicate-key-node.js';
import { CreateViewNode } from './create-view-node.js';
import { DropViewNode } from './drop-view-node.js';
import { GeneratedNode } from './generated-node.js';
import { DefaultValueNode } from './default-value-node.js';
import { OnNode } from './on-node.js';
import { ValuesNode } from './values-node.js';
import { SelectModifierNode } from './select-modifier-node.js';
import { CreateTypeNode } from './create-type-node.js';
import { DropTypeNode } from './drop-type-node.js';
import { ExplainNode } from './explain-node.js';
import { SchemableIdentifierNode } from './schemable-identifier-node.js';
import { DefaultInsertValueNode } from './default-insert-value-node.js';
import { AggregateFunctionNode } from './aggregate-function-node.js';
import { OverNode } from './over-node.js';
import { PartitionByNode } from './partition-by-node.js';
import { PartitionByItemNode } from './partition-by-item-node.js';
import { SetOperationNode } from './set-operation-node.js';
import { BinaryOperationNode } from './binary-operation-node.js';
import { UnaryOperationNode } from './unary-operation-node.js';
import { UsingNode } from './using-node.js';
import { FunctionNode } from './function-node.js';
import { WhenNode } from './when-node.js';
import { CaseNode } from './case-node.js';
import { JSONReferenceNode } from './json-reference-node.js';
import { JSONPathNode } from './json-path-node.js';
import { JSONPathLegNode } from './json-path-leg-node.js';
import { JSONOperatorChainNode } from './json-operator-chain-node.js';
import { TupleNode } from './tuple-node.js';
import { MergeQueryNode } from './merge-query-node.js';
import { MatchedNode } from './matched-node.js';
import { AddIndexNode } from './add-index-node.js';
import { CastNode } from './cast-node.js';
import { FetchNode } from './fetch-node.js';
import { TopNode } from './top-node.js';
import { OutputNode } from './output-node.js';
import { RefreshMaterializedViewNode } from './refresh-materialized-view-node.js';
import { OrActionNode } from './or-action-node.js';
import { CollateNode } from './collate-node.js';
import { RenameConstraintNode } from './rename-constraint-node.js';
export declare abstract class OperationNodeVisitor {
    #private;
    protected readonly nodeStack: OperationNode[];
    protected get parentNode(): OperationNode | undefined;
    protected readonly visitNode: (node: OperationNode) => void;
    protected abstract visitSelectQuery(node: SelectQueryNode): void;
    protected abstract visitSelection(node: SelectionNode): void;
    protected abstract visitColumn(node: ColumnNode): void;
    protected abstract visitAlias(node: AliasNode): void;
    protected abstract visitTable(node: TableNode): void;
    protected abstract visitFrom(node: FromNode): void;
    protected abstract visitReference(node: ReferenceNode): void;
    protected abstract visitAnd(node: AndNode): void;
    protected abstract visitOr(node: OrNode): void;
    protected abstract visitValueList(node: ValueListNode): void;
    protected abstract visitParens(node: ParensNode): void;
    protected abstract visitJoin(node: JoinNode): void;
    protected abstract visitRaw(node: RawNode): void;
    protected abstract visitWhere(node: WhereNode): void;
    protected abstract visitInsertQuery(node: InsertQueryNode): void;
    protected abstract visitDeleteQuery(node: DeleteQueryNode): void;
    protected abstract visitReturning(node: ReturningNode): void;
    protected abstract visitCreateTable(node: CreateTableNode): void;
    protected abstract visitAddColumn(node: AddColumnNode): void;
    protected abstract visitColumnDefinition(node: ColumnDefinitionNode): void;
    protected abstract visitDropTable(node: DropTableNode): void;
    protected abstract visitOrderBy(node: OrderByNode): void;
    protected abstract visitOrderByItem(node: OrderByItemNode): void;
    protected abstract visitGroupBy(node: GroupByNode): void;
    protected abstract visitGroupByItem(node: GroupByItemNode): void;
    protected abstract visitUpdateQuery(node: UpdateQueryNode): void;
    protected abstract visitColumnUpdate(node: ColumnUpdateNode): void;
    protected abstract visitLimit(node: LimitNode): void;
    protected abstract visitOffset(node: OffsetNode): void;
    protected abstract visitOnConflict(node: OnConflictNode): void;
    protected abstract visitOnDuplicateKey(node: OnDuplicateKeyNode): void;
    protected abstract visitCreateIndex(node: CreateIndexNode): void;
    protected abstract visitDropIndex(node: DropIndexNode): void;
    protected abstract visitList(node: ListNode): void;
    protected abstract visitPrimaryKeyConstraint(node: PrimaryKeyConstraintNode): void;
    protected abstract visitUniqueConstraint(node: UniqueConstraintNode): void;
    protected abstract visitReferences(node: ReferencesNode): void;
    protected abstract visitCheckConstraint(node: CheckConstraintNode): void;
    protected abstract visitWith(node: WithNode): void;
    protected abstract visitCommonTableExpression(node: CommonTableExpressionNode): void;
    protected abstract visitCommonTableExpressionName(node: CommonTableExpressionNameNode): void;
    protected abstract visitHaving(node: HavingNode): void;
    protected abstract visitCreateSchema(node: CreateSchemaNode): void;
    protected abstract visitDropSchema(node: DropSchemaNode): void;
    protected abstract visitAlterTable(node: AlterTableNode): void;
    protected abstract visitDropColumn(node: DropColumnNode): void;
    protected abstract visitRenameColumn(node: RenameColumnNode): void;
    protected abstract visitAlterColumn(node: AlterColumnNode): void;
    protected abstract visitModifyColumn(node: ModifyColumnNode): void;
    protected abstract visitAddConstraint(node: AddConstraintNode): void;
    protected abstract visitDropConstraint(node: DropConstraintNode): void;
    protected abstract visitRenameConstraint(node: RenameConstraintNode): void;
    protected abstract visitForeignKeyConstraint(node: ForeignKeyConstraintNode): void;
    protected abstract visitDataType(node: DataTypeNode): void;
    protected abstract visitSelectAll(node: SelectAllNode): void;
    protected abstract visitIdentifier(node: IdentifierNode): void;
    protected abstract visitSchemableIdentifier(node: SchemableIdentifierNode): void;
    protected abstract visitValue(node: ValueNode): void;
    protected abstract visitPrimitiveValueList(node: PrimitiveValueListNode): void;
    protected abstract visitOperator(node: OperatorNode): void;
    protected abstract visitCreateView(node: CreateViewNode): void;
    protected abstract visitRefreshMaterializedView(node: RefreshMaterializedViewNode): void;
    protected abstract visitDropView(node: DropViewNode): void;
    protected abstract visitGenerated(node: GeneratedNode): void;
    protected abstract visitDefaultValue(node: DefaultValueNode): void;
    protected abstract visitOn(node: OnNode): void;
    protected abstract visitValues(node: ValuesNode): void;
    protected abstract visitSelectModifier(node: SelectModifierNode): void;
    protected abstract visitCreateType(node: CreateTypeNode): void;
    protected abstract visitDropType(node: DropTypeNode): void;
    protected abstract visitExplain(node: ExplainNode): void;
    protected abstract visitDefaultInsertValue(node: DefaultInsertValueNode): void;
    protected abstract visitAggregateFunction(node: AggregateFunctionNode): void;
    protected abstract visitOver(node: OverNode): void;
    protected abstract visitPartitionBy(node: PartitionByNode): void;
    protected abstract visitPartitionByItem(node: PartitionByItemNode): void;
    protected abstract visitSetOperation(node: SetOperationNode): void;
    protected abstract visitBinaryOperation(node: BinaryOperationNode): void;
    protected abstract visitUnaryOperation(node: UnaryOperationNode): void;
    protected abstract visitUsing(node: UsingNode): void;
    protected abstract visitFunction(node: FunctionNode): void;
    protected abstract visitCase(node: CaseNode): void;
    protected abstract visitWhen(node: WhenNode): void;
    protected abstract visitJSONReference(node: JSONReferenceNode): void;
    protected abstract visitJSONPath(node: JSONPathNode): void;
    protected abstract visitJSONPathLeg(node: JSONPathLegNode): void;
    protected abstract visitJSONOperatorChain(node: JSONOperatorChainNode): void;
    protected abstract visitTuple(node: TupleNode): void;
    protected abstract visitMergeQuery(node: MergeQueryNode): void;
    protected abstract visitMatched(node: MatchedNode): void;
    protected abstract visitAddIndex(node: AddIndexNode): void;
    protected abstract visitCast(node: CastNode): void;
    protected abstract visitFetch(node: FetchNode): void;
    protected abstract visitTop(node: TopNode): void;
    protected abstract visitOutput(node: OutputNode): void;
    protected abstract visitOrAction(node: OrActionNode): void;
    protected abstract visitCollate(node: CollateNode): void;
}
